#!/bin/bash

echo "Running evaluations for with_gt data..."
for dir in outputs/with_gt/*/; do
    if [ -d "$dir" ]; then
        echo "Processing directory: $dir"
        for file in "$dir"*.txt; do
            if [ -f "$file" ] && [[ ! $(basename "$file") == evaluation_* ]]; then
                echo "Evaluating: $file"
                python3 evaluate_v2.py --eval_file "$file"
                # Move the result to the correct directory
                eval_result="outputs/evaluation_$(basename "$file" .txt).txt"
                if [ -f "$eval_result" ]; then
                    mv "$eval_result" "$dir"
                fi
            fi
        done
    fi
done

echo "Running evaluations for without_gt data..."
for dir in outputs/without_gt/*/; do
    if [ -d "$dir" ]; then
        echo "Processing directory: $dir"
        for file in "$dir"*.txt; do
            if [ -f "$file" ] && [[ ! $(basename "$file") == evaluation_* ]]; then
                echo "Evaluating: $file"
                python3 evaluate_v2.py --eval_file "$file"
                # Move the result to the correct directory
                eval_result="outputs/evaluation_$(basename "$file" .txt).txt"
                if [ -f "$eval_result" ]; then
                    mv "$eval_result" "$dir"
                fi
            fi
        done
    fi
done

echo "All evaluations completed!"

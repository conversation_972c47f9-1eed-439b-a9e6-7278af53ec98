# 批量评估脚本使用说明

## 修改内容

### 1. 修改 `evaluate_v2.py`
- 添加了 `--output_dir` 参数，允许指定输出目录
- 现在可以将评估结果保存到指定的目录中

### 2. 新建 `run_evaluate.py`
- 自动批量处理 `outputs/with_gt/` 和 `outputs/without_gt/` 下的所有 `.txt` 文件
- 自动在每个对应目录下生成 `evaluation_*.txt` 结果文件

## 使用方法

### 方法1：批量运行（推荐）
```bash
python3 run_evaluate.py
```

这个脚本会：
1. 自动扫描 `outputs/with_gt/` 和 `outputs/without_gt/` 下的所有子目录
2. 找到所有非 `evaluation_` 开头的 `.txt` 文件
3. 对每个文件运行评估
4. 将结果保存在同一目录下，文件名格式为 `evaluation_*.txt`

### 方法2：单独运行
```bash
python3 evaluate_v2.py --eval_file outputs/with_gt/1/step_by_step_gpt-4o_handcrafted.txt --output_dir outputs/with_gt/1/
```

## 目录结构
```
outputs/
├── with_gt/
│   ├── 1/
│   │   ├── step_by_step_gpt-4o_handcrafted.txt          # 原始文件
│   │   ├── evaluation_step_by_step_gpt-4o_handcrafted.txt  # 生成的评估结果
│   │   └── ...
│   ├── 2/
│   └── ...
└── without_gt/
    ├── 1/
    ├── 2/
    └── ...
```

## 注意事项
- `with_gt` 数据使用 `../Who&When/Algorithm-Generated` 作为 ground truth 路径
- `without_gt` 数据目前也使用相同路径（可根据需要调整）
- 评估结果文件会自动保存在对应的子目录中
- 脚本会跳过已存在的 `evaluation_*.txt` 文件，避免重复处理

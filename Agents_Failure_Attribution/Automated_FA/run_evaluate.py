#!/usr/bin/env python3
"""
Batch evaluation script for with_gt and without_gt data.
Automatically runs evaluation for all .txt files in the outputs directories.
"""

import os
import subprocess
import sys
from pathlib import Path

def find_eval_files(base_dir):
    """Find all .txt files in the outputs directory structure."""
    eval_files = []
    
    for root, dirs, files in os.walk(base_dir):
        for file in files:
            if file.endswith('.txt') and not file.startswith('evaluation_'):
                eval_files.append(os.path.join(root, file))
    
    return eval_files

def get_data_path_for_type(eval_type):
    """Get the appropriate data path based on evaluation type."""
    if eval_type == "with_gt":
        return "../Who&When/Algorithm-Generated"
    elif eval_type == "without_gt":
        # For without_gt, we might not have ground truth, but we still need a path
        # You may need to adjust this based on your actual data structure
        return "../Who&When/Algorithm-Generated"  # or another appropriate path
    else:
        return "../Who&When/Algorithm-Generated"

def run_evaluation(eval_file, data_path, output_dir):
    """Run evaluation for a single file."""
    print(f"\n{'='*60}")
    print(f"Processing: {eval_file}")
    print(f"Data path: {data_path}")
    print(f"Output dir: {output_dir}")
    print(f"{'='*60}")
    
    cmd = [
        sys.executable, "evaluate_v2.py",
        "--eval_file", eval_file,
        "--data_path", data_path,
        "--output_dir", output_dir
    ]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ Evaluation completed successfully")
        if result.stdout:
            print("Output:", result.stdout)
    except subprocess.CalledProcessError as e:
        print(f"✗ Error running evaluation: {e}")
        if e.stdout:
            print("Stdout:", e.stdout)
        if e.stderr:
            print("Stderr:", e.stderr)
    except Exception as e:
        print(f"✗ Unexpected error: {e}")

def main():
    """Main function to run batch evaluation."""
    base_outputs_dir = "outputs"
    
    if not os.path.exists(base_outputs_dir):
        print(f"Error: Outputs directory '{base_outputs_dir}' not found!")
        return
    
    # Process both with_gt and without_gt
    for eval_type in ["with_gt", "without_gt"]:
        type_dir = os.path.join(base_outputs_dir, eval_type)
        
        if not os.path.exists(type_dir):
            print(f"Warning: Directory '{type_dir}' not found, skipping...")
            continue
        
        print(f"\n🔍 Processing {eval_type} data...")
        
        # Find all evaluation files in this type directory
        eval_files = find_eval_files(type_dir)
        
        if not eval_files:
            print(f"No .txt files found in {type_dir}")
            continue
        
        print(f"Found {len(eval_files)} evaluation files to process")
        
        # Get appropriate data path
        data_path = get_data_path_for_type(eval_type)
        
        # Process each evaluation file
        for eval_file in eval_files:
            # Determine output directory (same directory as the eval file)
            output_dir = os.path.dirname(eval_file)
            
            # Run evaluation
            run_evaluation(eval_file, data_path, output_dir)
    
    print(f"\n🎉 Batch evaluation completed!")
    print("Check the respective directories for evaluation_*.txt files")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to extract and summarize accuracy results from evaluation files.
Creates a table showing Agent-level and Step-level accuracy for different methods and data types.
"""

import os
import re
from pathlib import Path

def extract_accuracy_from_file(filepath):
    """Extract Agent Accuracy and Step Accuracy from evaluation file."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract Agent Accuracy
        agent_match = re.search(r'Agent Accuracy:\s*(\d+\.?\d*)%', content)
        agent_acc = float(agent_match.group(1)) if agent_match else None
        
        # Extract Step Accuracy  
        step_match = re.search(r'Step Accuracy:\s*(\d+\.?\d*)%', content)
        step_acc = float(step_match.group(1)) if step_match else None
        
        return agent_acc, step_acc
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
        return None, None

def parse_filename(filename):
    """Parse evaluation filename to extract method and data type."""
    # Remove 'evaluation_' prefix and '.txt' suffix
    name = filename.replace('evaluation_', '').replace('.txt', '')
    
    # Handle different naming patterns
    if 'all_at-once' in name or 'all_at_once' in name:
        method = 'all_at_once'
    elif 'step_by_step' in name:
        method = 'step_by_step'
    elif 'binary_search' in name:
        method = 'binary_search'
    else:
        method = 'unknown'
    
    # Determine data type
    if 'handcrafted' in name:
        data_type = 'handcrafted'
    elif 'alg_generated' in name or 'algorithm-generated' in name:
        data_type = 'algorithm_generated'
    else:
        data_type = 'unknown'
    
    return method, data_type

def main():
    # Directory containing evaluation files
    outputs_dir = Path('outputs')
    
    # Find all evaluation files in outputs/ (not in subdirectories)
    eval_files = []
    for file in outputs_dir.iterdir():
        if file.is_file() and file.name.startswith('evaluation_'):
            eval_files.append(file)
    
    print(f"Found {len(eval_files)} evaluation files:")
    for f in eval_files:
        print(f"  - {f.name}")
    print()
    
    # Extract data from each file
    results = []
    for filepath in eval_files:
        method, data_type = parse_filename(filepath.name)
        agent_acc, step_acc = extract_accuracy_from_file(filepath)
        
        if agent_acc is not None and step_acc is not None:
            results.append({
                'method': method,
                'data_type': data_type,
                'agent_accuracy': agent_acc,
                'step_accuracy': step_acc,
                'filename': filepath.name
            })
            print(f"✓ {filepath.name}: {method} + {data_type} -> Agent: {agent_acc}%, Step: {step_acc}%")
        else:
            print(f"✗ Failed to extract data from {filepath.name}")
    
    if not results:
        print("No valid results found!")
        return

    # Organize data by method and data type
    # If there are duplicates, prefer the one with non-zero agent accuracy
    data_dict = {}
    for result in results:
        method = result['method']
        data_type = result['data_type']
        if method not in data_dict:
            data_dict[method] = {}

        # If this combination already exists, keep the one with higher agent accuracy
        if data_type in data_dict[method]:
            existing_agent_acc = data_dict[method][data_type]['agent_accuracy']
            current_agent_acc = result['agent_accuracy']
            if current_agent_acc > existing_agent_acc:
                data_dict[method][data_type] = {
                    'agent_accuracy': result['agent_accuracy'],
                    'step_accuracy': result['step_accuracy']
                }
        else:
            data_dict[method][data_type] = {
                'agent_accuracy': result['agent_accuracy'],
                'step_accuracy': result['step_accuracy']
            }

    print("\n" + "="*80)
    print("ACCURACY SUMMARY TABLE")
    print("="*80)

    # Define methods and data types
    methods = ['all_at_once', 'step_by_step', 'binary_search']
    data_types = ['handcrafted', 'algorithm_generated']

    # Agent-level accuracy table
    print("\n📊 AGENT-LEVEL ACCURACY (%)")
    print("-" * 60)
    print(f"{'Method':<15} {'Handcrafted':<15} {'Algorithm Generated':<20}")
    print("-" * 60)
    for method in methods:
        row = f"{method:<15}"
        for data_type in data_types:
            if method in data_dict and data_type in data_dict[method]:
                acc = data_dict[method][data_type]['agent_accuracy']
                row += f" {acc:>10.2f}%    "
            else:
                row += f" {'N/A':>10}     "
        print(row)

    # Step-level accuracy table
    print("\n📊 STEP-LEVEL ACCURACY (%)")
    print("-" * 60)
    print(f"{'Method':<15} {'Handcrafted':<15} {'Algorithm Generated':<20}")
    print("-" * 60)
    for method in methods:
        row = f"{method:<15}"
        for data_type in data_types:
            if method in data_dict and data_type in data_dict[method]:
                acc = data_dict[method][data_type]['step_accuracy']
                row += f" {acc:>10.2f}%    "
            else:
                row += f" {'N/A':>10}     "
        print(row)

    # Combined table
    print("\n📊 COMBINED RESULTS TABLE")
    print("-" * 80)
    print("Format: Agent% / Step%")
    print("-" * 80)
    print(f"{'Method':<15} {'Handcrafted':<20} {'Algorithm Generated':<25}")
    print("-" * 80)
    for method in methods:
        row = f"{method:<15}"
        for data_type in data_types:
            if method in data_dict and data_type in data_dict[method]:
                agent_acc = data_dict[method][data_type]['agent_accuracy']
                step_acc = data_dict[method][data_type]['step_accuracy']
                combined = f"{agent_acc:.2f}% / {step_acc:.2f}%"
                if data_type == 'handcrafted':
                    row += f" {combined:<18}"
                else:
                    row += f" {combined:<23}"
            else:
                if data_type == 'handcrafted':
                    row += f" {'N/A':<18}"
                else:
                    row += f" {'N/A':<23}"
        print(row)

    # Save detailed results to CSV
    output_csv = 'accuracy_summary.csv'
    with open(output_csv, 'w') as f:
        f.write("method,data_type,agent_accuracy,step_accuracy,filename\n")
        for result in results:
            f.write(f"{result['method']},{result['data_type']},{result['agent_accuracy']},{result['step_accuracy']},{result['filename']}\n")
    print(f"\n💾 Detailed results saved to: {output_csv}")

    # Save summary tables
    with open('accuracy_tables.txt', 'w') as f:
        f.write("AGENT-LEVEL ACCURACY (%)\n")
        f.write("-" * 60 + "\n")
        f.write(f"{'Method':<15} {'Handcrafted':<15} {'Algorithm Generated':<20}\n")
        f.write("-" * 60 + "\n")
        for method in methods:
            row = f"{method:<15}"
            for data_type in data_types:
                if method in data_dict and data_type in data_dict[method]:
                    acc = data_dict[method][data_type]['agent_accuracy']
                    row += f" {acc:>10.2f}%    "
                else:
                    row += f" {'N/A':>10}     "
            f.write(row + "\n")

        f.write("\n\nSTEP-LEVEL ACCURACY (%)\n")
        f.write("-" * 60 + "\n")
        f.write(f"{'Method':<15} {'Handcrafted':<15} {'Algorithm Generated':<20}\n")
        f.write("-" * 60 + "\n")
        for method in methods:
            row = f"{method:<15}"
            for data_type in data_types:
                if method in data_dict and data_type in data_dict[method]:
                    acc = data_dict[method][data_type]['step_accuracy']
                    row += f" {acc:>10.2f}%    "
                else:
                    row += f" {'N/A':>10}     "
            f.write(row + "\n")

        f.write("\n\nCOMBINED RESULTS (Agent% / Step%)\n")
        f.write("-" * 80 + "\n")
        f.write(f"{'Method':<15} {'Handcrafted':<20} {'Algorithm Generated':<25}\n")
        f.write("-" * 80 + "\n")
        for method in methods:
            row = f"{method:<15}"
            for data_type in data_types:
                if method in data_dict and data_type in data_dict[method]:
                    agent_acc = data_dict[method][data_type]['agent_accuracy']
                    step_acc = data_dict[method][data_type]['step_accuracy']
                    combined = f"{agent_acc:.2f}% / {step_acc:.2f}%"
                    if data_type == 'handcrafted':
                        row += f" {combined:<18}"
                    else:
                        row += f" {combined:<23}"
                else:
                    if data_type == 'handcrafted':
                        row += f" {'N/A':<18}"
                    else:
                        row += f" {'N/A':<23}"
            f.write(row + "\n")

    print(f"📄 Summary tables saved to: accuracy_tables.txt")

if __name__ == "__main__":
    main()

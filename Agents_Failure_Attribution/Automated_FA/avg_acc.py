#!/usr/bin/env python3
"""
Script to extract and summarize accuracy results from evaluation files for without_gt data.
Creates combined results tables for each run and overall average summary.
"""

import os
import re
import sys
from pathlib import Path
import statistics

def extract_accuracy_from_file(filepath):
    """Extract Agent Accuracy and Step Accuracy from evaluation file."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract Agent Accuracy
        agent_match = re.search(r'Agent Accuracy:\s*(\d+\.?\d*)%', content)
        agent_acc = float(agent_match.group(1)) if agent_match else None
        
        # Extract Step Accuracy  
        step_match = re.search(r'Step Accuracy:\s*(\d+\.?\d*)%', content)
        step_acc = float(step_match.group(1)) if step_match else None
        
        return agent_acc, step_acc
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
        return None, None

def parse_filename(filename):
    """Parse evaluation filename to extract method and data type."""
    # Remove 'evaluation_' prefix and '.txt' suffix
    name = filename.replace('evaluation_', '').replace('.txt', '')
    
    # Handle different naming patterns
    if 'all_at-once' in name or 'all_at_once' in name:
        method = 'all_at_once'
    elif 'step_by_step' in name:
        method = 'step_by_step'
    elif 'binary_search' in name:
        method = 'binary_search'
    else:
        method = 'unknown'
    
    # Determine data type
    if 'handcrafted' in name:
        data_type = 'handcrafted'
    elif 'alg_generated' in name or 'algorithm-generated' in name:
        data_type = 'algorithm_generated'
    else:
        data_type = 'unknown'
    
    return method, data_type

def save_combined_table(data_dict, output_path):
    """Save combined results table to file."""
    methods = ['all_at_once', 'step_by_step', 'binary_search']
    data_types = ['handcrafted', 'algorithm_generated']

    with open(output_path, 'w') as f:
        f.write("COMBINED RESULTS TABLE (Agent% / Step%)\n")
        f.write("-" * 80 + "\n")
        f.write(f"{'Method':<15} {'Handcrafted':<20} {'Algorithm Generated':<25}\n")
        f.write("-" * 80 + "\n")

        for method in methods:
            row = f"{method:<15}"
            for data_type in data_types:
                if method in data_dict and data_type in data_dict[method]:
                    agent_acc = data_dict[method][data_type]['agent_accuracy']
                    step_acc = data_dict[method][data_type]['step_accuracy']
                    combined = f"{agent_acc:.2f}% / {step_acc:.2f}%"
                    if data_type == 'handcrafted':
                        row += f" {combined:<18}"
                    else:
                        row += f" {combined:<23}"
                else:
                    if data_type == 'handcrafted':
                        row += f" {'N/A':<18}"
                    else:
                        row += f" {'N/A':<23}"
            f.write(row + "\n")

def process_run_directory(run_dir):
    """Process evaluation files in a single run directory."""
    print(f"\n🔍 Processing {run_dir}...")

    # Find all evaluation files in this run directory
    eval_files = []
    for file in run_dir.iterdir():
        if file.is_file() and file.name.startswith('evaluation_'):
            eval_files.append(file)

    if not eval_files:
        print(f"  No evaluation files found in {run_dir}")
        return None

    print(f"  Found {len(eval_files)} evaluation files")

    # Extract data from each file
    results = []
    for filepath in eval_files:
        method, data_type = parse_filename(filepath.name)
        agent_acc, step_acc = extract_accuracy_from_file(filepath)

        if agent_acc is not None and step_acc is not None:
            results.append({
                'method': method,
                'data_type': data_type,
                'agent_accuracy': agent_acc,
                'step_accuracy': step_acc,
                'filename': filepath.name
            })
            print(f"    ✓ {method} + {data_type} -> Agent: {agent_acc}%, Step: {step_acc}%")
        else:
            print(f"    ✗ Failed to extract data from {filepath.name}")

    if not results:
        print(f"  No valid results found in {run_dir}")
        return None

    # Organize data by method and data type
    data_dict = {}
    for result in results:
        method = result['method']
        data_type = result['data_type']
        if method not in data_dict:
            data_dict[method] = {}

        # If this combination already exists, keep the one with higher agent accuracy
        if data_type in data_dict[method]:
            existing_agent_acc = data_dict[method][data_type]['agent_accuracy']
            current_agent_acc = result['agent_accuracy']
            if current_agent_acc > existing_agent_acc:
                data_dict[method][data_type] = {
                    'agent_accuracy': result['agent_accuracy'],
                    'step_accuracy': result['step_accuracy']
                }
        else:
            data_dict[method][data_type] = {
                'agent_accuracy': result['agent_accuracy'],
                'step_accuracy': result['step_accuracy']
            }

    # Save combined table in this run directory
    table_path = run_dir / 'combined_results.txt'
    save_combined_table(data_dict, table_path)
    print(f"  💾 Combined results saved to: {table_path}")

    return data_dict

def main():
    """Main function to process evaluation data."""
    # Check command line arguments
    if len(sys.argv) > 1:
        target_dir_name = sys.argv[1]
    else:
        target_dir_name = 'without_gt'  # default

    # Check if we're already in the target directory
    current_dir = Path.cwd()
    if current_dir.name == target_dir_name:
        target_dir = current_dir
    else:
        target_dir = Path(f'outputs/{target_dir_name}')

    if not target_dir.exists():
        print(f"Error: Directory {target_dir} not found!")
        return

    print(f"🚀 Processing {target_dir_name} evaluation results...")

    # Find all run directories (1, 2, 3, etc.)
    run_dirs = []
    for item in target_dir.iterdir():
        if item.is_dir() and item.name.isdigit():
            run_dirs.append(item)

    run_dirs.sort(key=lambda x: int(x.name))

    if not run_dirs:
        print(f"No run directories found in {target_dir}")
        return

    print(f"Found {len(run_dirs)} run directories: {[d.name for d in run_dirs]}")

    # Process each run directory
    all_run_results = []
    for run_dir in run_dirs:
        run_data = process_run_directory(run_dir)
        if run_data:
            all_run_results.append(run_data)

    if not all_run_results:
        print("No valid results found in any run directory!")
        return

    # Calculate average accuracies across all runs
    print(f"\n📊 Calculating averages across {len(all_run_results)} runs...")

    methods = ['all_at_once', 'step_by_step', 'binary_search']
    data_types = ['handcrafted', 'algorithm_generated']

    avg_results = {}
    for method in methods:
        avg_results[method] = {}
        for data_type in data_types:
            agent_accs = []
            step_accs = []

            for run_data in all_run_results:
                if method in run_data and data_type in run_data[method]:
                    agent_accs.append(run_data[method][data_type]['agent_accuracy'])
                    step_accs.append(run_data[method][data_type]['step_accuracy'])

            if agent_accs and step_accs:
                avg_results[method][data_type] = {
                    'agent_accuracy': statistics.mean(agent_accs),
                    'step_accuracy': statistics.mean(step_accs),
                    'agent_std': statistics.stdev(agent_accs) if len(agent_accs) > 1 else 0,
                    'step_std': statistics.stdev(step_accs) if len(step_accs) > 1 else 0,
                    'count': len(agent_accs)
                }

    # Save average results summary
    summary_path = target_dir / 'average_results_summary.txt'
    save_average_summary(avg_results, summary_path)
    print(f"\n💾 Average results summary saved to: {summary_path}")

    # Print summary to console
    print_average_summary(avg_results)

def save_average_summary(avg_results, output_path):
    """Save average results summary to file."""
    methods = ['all_at_once', 'step_by_step', 'binary_search']
    data_types = ['handcrafted', 'algorithm_generated']

    with open(output_path, 'w') as f:
        f.write("AVERAGE RESULTS ACROSS ALL RUNS (Agent% / Step%)\n")
        f.write("=" * 80 + "\n")
        f.write(f"{'Method':<15} {'Handcrafted':<25} {'Algorithm Generated':<30}\n")
        f.write("-" * 80 + "\n")

        for method in methods:
            row = f"{method:<15}"
            for data_type in data_types:
                if method in avg_results and data_type in avg_results[method]:
                    data = avg_results[method][data_type]
                    agent_avg = data['agent_accuracy']
                    step_avg = data['step_accuracy']
                    agent_std = data['agent_std']
                    step_std = data['step_std']
                    count = data['count']

                    combined = f"{agent_avg:.2f}±{agent_std:.2f} / {step_avg:.2f}±{step_std:.2f}"
                    if data_type == 'handcrafted':
                        row += f" {combined:<23}"
                    else:
                        row += f" {combined:<28}"
                else:
                    if data_type == 'handcrafted':
                        row += f" {'N/A':<23}"
                    else:
                        row += f" {'N/A':<28}"
            f.write(row + "\n")

        f.write("\nNote: Format is Mean±StdDev for Agent% / Step%\n")

def print_average_summary(avg_results):
    """Print average results summary to console."""
    methods = ['all_at_once', 'step_by_step', 'binary_search']
    data_types = ['handcrafted', 'algorithm_generated']

    print("\n" + "="*80)
    print("AVERAGE RESULTS ACROSS ALL RUNS")
    print("="*80)
    print(f"{'Method':<15} {'Handcrafted':<25} {'Algorithm Generated':<30}")
    print("-" * 80)

    for method in methods:
        row = f"{method:<15}"
        for data_type in data_types:
            if method in avg_results and data_type in avg_results[method]:
                data = avg_results[method][data_type]
                agent_avg = data['agent_accuracy']
                step_avg = data['step_accuracy']
                agent_std = data['agent_std']
                step_std = data['step_std']

                combined = f"{agent_avg:.2f}±{agent_std:.2f} / {step_avg:.2f}±{step_std:.2f}"
                if data_type == 'handcrafted':
                    row += f" {combined:<23}"
                else:
                    row += f" {combined:<28}"
            else:
                if data_type == 'handcrafted':
                    row += f" {'N/A':<23}"
                else:
                    row += f" {'N/A':<28}"
        print(row)

    print("\nNote: Format is Mean±StdDev for Agent% / Step%")

if __name__ == "__main__":
    main()
